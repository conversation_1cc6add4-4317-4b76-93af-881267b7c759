#!/bin/bash

# Local development server startup script
# This script sets up the environment and runs the Django development server

echo "Starting Django development server..."

# Set Python path to include virtual environment packages
export PYTHONPATH="venv/lib/python3.12/site-packages:$PYTHONPATH"

# Collect static files
echo "Collecting static files..."
python3 manage.py collectstatic --noinput

# Run migrations
echo "Running migrations..."
python3 manage.py migrate

# Start the development server
echo "Starting server at http://localhost:8000"
python3 manage.py runserver 0.0.0.0:8000
