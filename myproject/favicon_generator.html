<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Generator for Quick Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        .size-demo {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quick Receipt Favicon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click the "Generate Favicons" button below</li>
                <li>Right-click on each generated favicon and "Save image as..."</li>
                <li>Save them in your <code>myproject/users/static/users/assets/img/</code> directory</li>
                <li>The SVG favicon is already created in your project</li>
            </ol>
        </div>

        <button onclick="generateFavicons()">Generate Favicons</button>
        
        <div class="favicon-preview" id="faviconPreview">
            <!-- Generated favicons will appear here -->
        </div>

        <h3>Current SVG Favicon Preview:</h3>
        <div style="padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="64" height="64">
                <!-- Background circle -->
                <circle cx="16" cy="16" r="15" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
                
                <!-- Receipt paper -->
                <rect x="8" y="6" width="16" height="20" rx="1" fill="#ffffff" stroke="#e0e0e0" stroke-width="0.5"/>
                
                <!-- Receipt header lines -->
                <line x1="10" y1="9" x2="22" y2="9" stroke="#333333" stroke-width="0.8"/>
                <line x1="10" y1="11" x2="20" y2="11" stroke="#666666" stroke-width="0.6"/>
                
                <!-- Receipt content lines -->
                <line x1="10" y1="14" x2="22" y2="14" stroke="#999999" stroke-width="0.5"/>
                <line x1="10" y1="16" x2="18" y2="16" stroke="#999999" stroke-width="0.5"/>
                <line x1="10" y1="18" x2="21" y2="18" stroke="#999999" stroke-width="0.5"/>
                <line x1="10" y1="20" x2="16" y2="20" stroke="#999999" stroke-width="0.5"/>
                
                <!-- Total line (thicker) -->
                <line x1="10" y1="22" x2="22" y2="22" stroke="#333333" stroke-width="0.8"/>
                
                <!-- Small dollar sign -->
                <text x="19" y="17" font-family="Arial, sans-serif" font-size="4" fill="#007bff" font-weight="bold">$</text>
            </svg>
        </div>
    </div>

    <script>
        function generateFavicons() {
            const sizes = [16, 32, 48, 64];
            const preview = document.getElementById('faviconPreview');
            preview.innerHTML = '';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // Draw the favicon
                drawFavicon(ctx, size);

                const container = document.createElement('div');
                container.className = 'size-demo';
                container.innerHTML = `
                    <div>${size}x${size}px</div>
                    <div style="margin: 10px 0;">${canvas.outerHTML}</div>
                    <button onclick="downloadCanvas(this.previousElementSibling.querySelector('canvas'), 'favicon-${size}x${size}.png')">
                        Download ${size}x${size}
                    </button>
                `;
                preview.appendChild(container);
            });
        }

        function drawFavicon(ctx, size) {
            const scale = size / 32;
            
            // Background circle
            ctx.fillStyle = '#007bff';
            ctx.strokeStyle = '#0056b3';
            ctx.lineWidth = 1 * scale;
            ctx.beginPath();
            ctx.arc(16 * scale, 16 * scale, 15 * scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();

            // Receipt paper
            ctx.fillStyle = '#ffffff';
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 0.5 * scale;
            ctx.fillRect(8 * scale, 6 * scale, 16 * scale, 20 * scale);
            ctx.strokeRect(8 * scale, 6 * scale, 16 * scale, 20 * scale);

            // Receipt lines
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 0.8 * scale;
            ctx.beginPath();
            ctx.moveTo(10 * scale, 9 * scale);
            ctx.lineTo(22 * scale, 9 * scale);
            ctx.stroke();

            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 0.6 * scale;
            ctx.beginPath();
            ctx.moveTo(10 * scale, 11 * scale);
            ctx.lineTo(20 * scale, 11 * scale);
            ctx.stroke();

            // Content lines
            ctx.strokeStyle = '#999999';
            ctx.lineWidth = 0.5 * scale;
            
            const lines = [
                [10, 14, 22, 14],
                [10, 16, 18, 16],
                [10, 18, 21, 18],
                [10, 20, 16, 20]
            ];

            lines.forEach(([x1, y1, x2, y2]) => {
                ctx.beginPath();
                ctx.moveTo(x1 * scale, y1 * scale);
                ctx.lineTo(x2 * scale, y2 * scale);
                ctx.stroke();
            });

            // Total line
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = 0.8 * scale;
            ctx.beginPath();
            ctx.moveTo(10 * scale, 22 * scale);
            ctx.lineTo(22 * scale, 22 * scale);
            ctx.stroke();

            // Dollar sign
            ctx.fillStyle = '#007bff';
            ctx.font = `bold ${4 * scale}px Arial`;
            ctx.fillText('$', 19 * scale, 17 * scale);
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
