<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - QuickReceipt</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .email-container {
            background-color: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 2em;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1em;
        }
        
        .content {
            margin-bottom: 30px;
        }
        
        .greeting {
            font-size: 1.1em;
            margin-bottom: 20px;
        }
        
        .message {
            margin-bottom: 25px;
            line-height: 1.7;
        }
        
        .reset-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1em;
            margin: 20px 0;
            text-align: center;
        }
        
        .reset-button:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            text-decoration: none;
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 25px 0;
            border-left: 4px solid #f39c12;
        }
        
        .security-info h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 1em;
        }
        
        .security-info p {
            margin: 0;
            color: #856404;
            font-size: 0.9em;
        }
        
        .alternative-link {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-size: 0.9em;
        }
        
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        
        .footer p {
            margin: 5px 0;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-container {
                padding: 20px;
            }
            
            .reset-button {
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">QuickReceipt</div>
            <div class="subtitle">Password Reset Request</div>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{ user.first_name|default:user.username }},
            </div>
            
            <div class="message">
                You recently requested to reset your password for your QuickReceipt account. 
                We're here to help you get back into your account quickly and securely.
            </div>
            
            <div class="button-container">
                <a href="{{ reset_url }}" class="reset-button">Reset My Password</a>
            </div>
            
            <div class="security-info">
                <h3>🔒 Security Information</h3>
                <p>
                    This password reset link will expire in <strong>1 hour</strong> for your security. 
                    If you did not request this reset, please ignore this email and your password will remain unchanged.
                </p>
            </div>
            
            <div class="message">
                If you're having trouble clicking the button above, copy and paste the following link into your web browser:
            </div>
            
            <div class="alternative-link">
                {{ reset_url }}
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Best regards,</strong></p>
            <p>The QuickReceipt Team</p>
            <hr style="border: none; border-top: 1px solid #dee2e6; margin: 15px 0;">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>If you need help, please contact our support team.</p>
        </div>
    </div>
</body>
</html>
