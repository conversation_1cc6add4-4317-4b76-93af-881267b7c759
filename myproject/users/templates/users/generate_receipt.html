{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Generate Receipt</title>

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <link rel="stylesheet" href="{% static 'users/css/style.css' %}" />
    <style>
      /* General Styles */
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f4f7fb;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        margin: 0;
        color: #333;
        padding: 0 20px;
      }
      
      .container {
        max-width: 500px;
        background-color: #ffffff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        text-align: center;
      }
      
      h1 {
        margin-bottom: 20px;
        font-size: 2.2rem;
        color: #007bff;
        font-weight: bold;
      }
      
      form {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      
      label {
        text-align: left;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 5px;
      }
      
      input,
      select,
      button {
        width: 100%;
        padding: 10px;
        font-size: 1rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        transition: all 0.3s ease;
      }
      
      input:focus,
      select:focus {
        border-color: #007bff;
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.2);
        outline: none;
      }
      
      button {
        background-color: #007bff;
        color: #fff;
        font-weight: bold;
        cursor: pointer;
        border: none;
      }
      
      button:hover {
        background-color: #0056b3;
      }
      
      .back-btn {
        background-color: #f8f9fa;
        color: #007bff;
        border: 1px solid #007bff;
      }
      
      .back-btn:hover {
        background-color: #e9ecef;
        color: #0056b3;
      }
      
      /* Table Styles */
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      
      th,
      td {
        padding: 12px 15px; /* Better spacing between columns */
        text-align: left;
        border: 1px solid #ddd;
      }
      
      th {
        background-color: #f8f9fa;
        font-weight: bold;
      }
      
      td input {
        width: calc(100% - 20px); /* Maintain neat input spacing */
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
      }
      
      td input:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 4px rgba(0, 123, 255, 0.2);
      }
      
      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .remove-btn {
        color: #e3342f;
        font-size: 1.2rem;
        background: none;
        border: none;
        cursor: pointer;
        transition: color 0.3s ease;
        padding: 0;
        text-align: center;
      }
      
      .remove-btn:hover {
        color: #c51f1a;
      }
      
      /* Responsive Design */
      @media (max-width: 768px) {
        th,
        td {
          padding: 10px 8px;
        }
      
        td input {
          font-size: 0.9rem;
          padding: 6px;
        }
      
        h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      {% include 'users/components/back_button.html' with fallback_url='dashboard' button_text='Back to Dashboard' %}

      <h1>Generate Receipt</h1>
      <form method="POST" onsubmit="prepareProductsJson()">
        {% csrf_token %}
        <div>
          <label for="id_customer_name">Customer Name:</label>
          {{ form.customer_name }}
        </div>

        <table id="products-table">
          <thead>
            <tr>
              <th>Product</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody id="products-container">
            <tr class="product-item">
              <td>
                <input type="text" name="product_bought" placeholder="Enter product name" required />
              </td>
              <td>
                <input type="number" name="quantity" min="1" placeholder="Qty" required />
              </td>
              <td>
                <input type="number" name="price" step="0.01" placeholder="Price" required />
              </td>
              <td>
                <button type="button" class="remove-btn" onclick="removeProduct(this)">&#10005;</button>
              </td>
            </tr>
          </tbody>
        </table>

        <button type="button" onclick="addProduct()">Add Product</button>

        <input type="hidden" name="products" id="products-json" />

        <div>
          <label for="id_discount">Discount:</label>
          {{ form.discount }}
        </div>

        <div>
          <label for="id_method_of_purchase">Method of Purchase:</label>
          {{ form.method_of_purchase }}
        </div>

        <div>
          <label for="id_date_of_purchase">Date of Purchase:</label>
          {{ form.date_of_purchase }}
        </div>

        <button type="submit">Generate Receipt</button>
      </form>
    </div>

    <script>
      // Add a new product row
      function addProduct() {
        const container = document.getElementById('products-container')
        const productRow = document.createElement('tr')
        productRow.className = 'product-item'
        productRow.innerHTML = `
                      <td><input type="text" name="product_bought" placeholder="Enter product name" required></td>
                      <td><input type="number" name="quantity" min="1" placeholder="Qty" required></td>
                      <td><input type="number" name="price" step="0.01" placeholder="Price" required></td>
                      <td>
                          <button type="button" class="remove-btn" onclick="removeProduct(this)">
                              &#10005;
                          </button>
                      </td>
                  `
        container.appendChild(productRow)
      }
      
      // Remove a product row
      function removeProduct(button) {
        const row = button.closest('tr')
        row.remove()
      }
      
      // Prepare JSON for products
      function prepareProductsJson() {
        const products = []
        const productItems = document.querySelectorAll('.product-item')
        productItems.forEach((item) => {
          const product = {
            product_bought: item.querySelector('input[name="product_bought"]').value,
            quantity: parseInt(item.querySelector('input[name="quantity"]').value),
            price: parseFloat(item.querySelector('input[name="price"]').value)
          }
          products.push(product)
        })
        document.getElementById('products-json').value = JSON.stringify(products)
      }
    </script>
  </body>
</html>
