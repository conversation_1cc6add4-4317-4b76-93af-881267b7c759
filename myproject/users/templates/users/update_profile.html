{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    {% load static %}
    <link rel="stylesheet" href="{% static 'users/css/style.css' %}" />
    <title>Update Profile</title>
    <style>
      /* General Body Styles */
      body {
        background: linear-gradient(to bottom, #f4f7fc, #dfe7f4);
        font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        padding: 40px;
        margin: 0;
      }
      
      .form-container {
        max-width: 600px;
        margin: 40px auto;
        background: #ffffff;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        animation: fadeIn 0.5s ease-in-out;
      }
      
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      h2 {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 25px;
        font-size: 1.8rem;
        font-weight: bold;
      }
      
      label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #34495e;
        display: block;
        font-size: 0.95rem;
      }
      
      input[type='text'],
      textarea,
      select {
        width: 100%;
        padding: 12px;
        margin-bottom: 20px;
        border: 1px solid #d1d3d4;
        border-radius: 6px;
        box-sizing: border-box;
        background-color: #f9fbfc;
        font-size: 1rem;
        transition: border-color 0.3s ease, background-color 0.3s ease;
      }
      
      input[type='text']:focus,
      textarea:focus,
      select:focus {
        border-color: #3498db;
        outline: none;
        background-color: #fff;
      }
      
      button {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 14px 20px;
        font-size: 1rem;
        font-weight: bold;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        width: 100%;
        margin-top: 10px;
      }
      
      button:hover {
        background-color: #2980b9;
      }
      
      .errorlist {
        color: #e74c3c;
        font-size: 0.9rem;
        margin-top: -10px;
        margin-bottom: 20px;
      }
      
      .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        font-size: 0.9rem;
      }
      
      .back-link a {
        color: #3498db;
        text-decoration: none;
        font-weight: bold;
      }
      
      .back-link a:hover {
        text-decoration: underline;
      }
      
      /* Responsive Design */
      @media (max-width: 768px) {
        body {
          padding: 20px;
        }
      
        .form-container {
          padding: 20px;
          width: 100%;
        }
      
        button {
          font-size: 0.9rem;
          padding: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="form-container">
      {% include 'users/components/back_button.html' with fallback_url='welcome' button_text='Back' %}

      <h2>Update Profile</h2>
      <form method="POST" enctype="multipart/form-data">
        {% csrf_token %}

        <label for="company_logo">Company Logo:</label>
        {{ form.company_logo }}
        {% if form.company_logo.errors %}
          <div class="errorlist">{{ form.company_logo.errors }}</div>
        {% endif %}

        <label for="company_name">Company Name:</label>
        {{ form.company_name }}

        <label for="company_details">Company Details:</label>
        {{ form.company_details }}

        <!-- New Address Field -->
        <label for="address">Address:</label>
        {{ form.address }}

        <!-- New WhatsApp Contact Field -->
        <label for="whatsapp_contact">WhatsApp Contact:</label>
        {{ form.whatsapp_contact }}

        <button type="submit">Update Profile</button>
      </form>
    </div>
  </body>
</html>
