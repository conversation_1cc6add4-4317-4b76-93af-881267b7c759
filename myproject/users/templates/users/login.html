{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <title>Login - Your Business Hub</title>
    <style>
        /* Reset default styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #00c6ff, #007bff);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .container {
            width: 100%;
            max-width: 450px;
            background-color: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        input[type="text"],
        input[type="password"] {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            background-color: #f9f9f9;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus,
        input[type="password"]:focus {
            border-color: #007bff;
            outline: none;
        }

        button {
            padding: 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        .footer-text {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .footer-text a {
            color: #007bff;
            text-decoration: none;
        }

        .footer-text a:hover {
            color: #0056b3;
        }

        .forgot-password-text {
            text-align: center;
            margin: 15px 0;
            font-size: 0.9em;
        }

        .forgot-password-text a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-password-text a:hover {
            text-decoration: underline;
            color: #0056b3;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 30px;
            }

            h2 {
                font-size: 1.8em;
            }

            button {
                font-size: 1em;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>Login to Your Account</h2>
        <form method="POST">
            <!-- CSRF Token and Form Fields -->
            {% csrf_token %}
            <p style="color: red;">{{ errors }}</p>

            <input type="text" name="username" placeholder="Username" required>
            <input type="password" name="password" placeholder="Password" required>

            <!-- Login button -->
            <button type="submit">Login</button>

            <!-- Forgot Password Link -->
            <p class="forgot-password-text">
                <a href="{% url 'password_reset_request' %}">Forgot your password?</a>
            </p>

            <!-- Footer text -->
            <p class="footer-text">Don't have an account? <a href="{% url 'signup' %}">Sign up here</a>.</p>
        </form>
    </div>

</body>
</html>
