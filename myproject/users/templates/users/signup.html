{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Your Business Hub</title>

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <style>
        /* Reset default styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #00c6ff, #007bff);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

        .container {
            width: 100%;
            max-width: 450px;
            background-color: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"] {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            background-color: #f9f9f9;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="password"]:focus {
            border-color: #007bff;
            outline: none;
        }

        button {
            padding: 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        .footer-text {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .footer-text a {
            color: #007bff;
            text-decoration: none;
        }

        .footer-text a:hover {
            color: #0056b3;
        }
        .errorlist {
            color: red;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 30px;
            }

            h2 {
                font-size: 1.8em;
            }

            button {
                font-size: 1em;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>Create Your Account</h2>
        <form method="POST">
            <!-- CSRF Token and Form Fields -->
            {% csrf_token %}

            <input type="text" name="username" placeholder="Username" required>
            {{ form.username.errors }}
            <input type="email" name="email" placeholder="Email Address" required>
            {{ form.email.errors }}
            <input type="password" name="password" placeholder="Password" required>
            {{ form.password.errors }}
            <input type="password" name="confirm_password" placeholder="Confirm Password" required>
            {{ form.confirm_password.errors }}

            <!-- Sign up button -->
            <button type="submit">Sign Up</button>

            <!-- Footer text -->
            <p class="footer-text">Already have an account? <a href="{% url 'login' %}">Login here</a>.</p>
        </form>
    </div>

</body>
</html>
