{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <title>Password Reset Sent - QuickReceipt</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 2.5em;
            color: white;
        }

        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2em;
            font-weight: 600;
        }

        .message {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .instructions h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .instructions ul {
            text-align: left;
            color: #555;
            line-height: 1.6;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .back-link {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .note {
            margin-top: 30px;
            font-size: 0.9em;
            color: #888;
            font-style: italic;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 30px;
                margin: 20px;
            }

            h2 {
                font-size: 1.8em;
            }

            .success-icon {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            ✓
        </div>
        
        <h2>Check Your Email</h2>
        
        <p class="message">
            We've sent password reset instructions to your email address. 
            Please check your inbox and follow the link to reset your password.
        </p>

        <div class="instructions">
            <h3>What to do next:</h3>
            <ul>
                <li>Check your email inbox (and spam folder)</li>
                <li>Click the reset link in the email</li>
                <li>Create a new password</li>
                <li>Log in with your new password</li>
            </ul>
        </div>

        <a href="{% url 'login' %}" class="back-link">Back to Login</a>

        <p class="note">
            Didn't receive the email? Check your spam folder or try requesting another reset.
        </p>
    </div>
</body>
</html>
