<!-- Back Button Component -->
<!-- Usage:  include 'users/components/back_button.html' with fallback_url='dashboard' button_text='Back' show_icon=True -->

{% load static %}

<div class="back-button-container">
    <button type="button" class="back-button" id="backButton" 
            data-fallback-url="{% if fallback_url %}{% url fallback_url %}{% else %}{% url 'dashboard' %}{% endif %}">
        {% if show_icon|default:True %}
            <svg class="back-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        {% endif %}
        <span class="back-text">{{ button_text|default:"Back" }}</span>
    </button>
</div>

<style>
    .back-button-container {
        margin-bottom: 20px;
    }

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: transparent;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        color: #666;
        font-size: 0.9em;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .back-button:hover {
        background-color: #f8f9fa;
        border-color: #667eea;
        color: #667eea;
        transform: translateX(-2px);
    }

    .back-button:active {
        transform: translateX(0);
    }

    .back-icon {
        transition: transform 0.3s ease;
    }

    .back-button:hover .back-icon {
        transform: translateX(-2px);
    }

    .back-text {
        font-family: inherit;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .back-button {
            padding: 8px 12px;
            font-size: 0.85em;
        }
        
        .back-icon {
            width: 14px;
            height: 14px;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .back-button {
            border-color: #4a5568;
            color: #a0aec0;
        }
        
        .back-button:hover {
            background-color: #2d3748;
            border-color: #667eea;
            color: #667eea;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.getElementById('backButton');
        
        if (backButton) {
            backButton.addEventListener('click', function() {
                const fallbackUrl = this.getAttribute('data-fallback-url');
                
                // Check if there's browser history to go back to
                if (window.history.length > 1 && document.referrer) {
                    // Check if the referrer is from the same domain
                    const referrerDomain = new URL(document.referrer).hostname;
                    const currentDomain = window.location.hostname;
                    
                    if (referrerDomain === currentDomain) {
                        window.history.back();
                        return;
                    }
                }
                
                // Fallback to the specified URL
                window.location.href = fallbackUrl;
            });
        }
    });
</script>
