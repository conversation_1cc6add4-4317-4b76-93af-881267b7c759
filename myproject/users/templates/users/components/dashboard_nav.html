<!-- Dashboard Navigation Component -->

<div class="dashboard-nav-container">
    <nav class="breadcrumb-nav">
        <a href="{% url 'welcome' %}" class="nav-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Home
        </a>
        <span class="separator">›</span>
        <span class="current-page">Dashboard</span>
    </nav>
    
    <div class="quick-actions">
        <a href="{% url 'generate_receipt' %}" class="quick-action-btn primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            New Receipt
        </a>
        <a href="{% url 'update_profile' %}" class="quick-action-btn secondary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Profile
        </a>
    </div>
</div>

<style>
    .dashboard-nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding: 15px 0;
        border-bottom: 1px solid #e1e5e9;
    }

    .breadcrumb-nav {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9em;
    }

    .nav-item {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #667eea;
        text-decoration: none;
        padding: 6px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .nav-item:hover {
        background-color: #f8f9fa;
        color: #5a6fd8;
        text-decoration: none;
    }

    .separator {
        color: #adb5bd;
        font-weight: 300;
        font-size: 1.2em;
    }

    .current-page {
        color: #495057;
        font-weight: 600;
    }

    .quick-actions {
        display: flex;
        gap: 10px;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border-radius: 8px;
        text-decoration: none;
        font-size: 0.85em;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .quick-action-btn.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .quick-action-btn.primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .quick-action-btn.secondary {
        background: transparent;
        color: #6c757d;
        border-color: #dee2e6;
    }

    .quick-action-btn.secondary:hover {
        background-color: #f8f9fa;
        border-color: #adb5bd;
        color: #495057;
        text-decoration: none;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .dashboard-nav-container {
            flex-direction: column;
            gap: 15px;
            align-items: flex-start;
        }

        .breadcrumb-nav {
            font-size: 0.85em;
        }

        .quick-actions {
            width: 100%;
            justify-content: space-between;
        }

        .quick-action-btn {
            flex: 1;
            justify-content: center;
            padding: 10px 12px;
        }
    }

    @media (max-width: 480px) {
        .quick-actions {
            flex-direction: column;
            gap: 8px;
        }

        .quick-action-btn {
            width: 100%;
        }
    }
</style>
