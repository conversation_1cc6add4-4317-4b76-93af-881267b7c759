{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <title>Password Reset Complete - QuickReceipt</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 3em;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }

        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.2em;
            font-weight: 600;
        }

        .message {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .success-details {
            background-color: #d4edda;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #28a745;
            text-align: left;
        }

        .success-details h3 {
            color: #155724;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .success-details ul {
            color: #155724;
            line-height: 1.6;
            margin: 0;
            padding-left: 20px;
        }

        .success-details li {
            margin-bottom: 8px;
        }

        .login-button {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1em;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 20px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .secondary-link {
            display: block;
            margin-top: 15px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .secondary-link:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .security-note {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 0.9em;
            color: #666;
            border-left: 4px solid #6c757d;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 30px;
                margin: 20px;
            }

            h2 {
                font-size: 1.8em;
            }

            .success-icon {
                width: 80px;
                height: 80px;
                font-size: 2.5em;
            }

            .login-button {
                padding: 12px 30px;
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            ✓
        </div>
        
        <h2>Password Reset Complete!</h2>
        
        <p class="message">
            Your password has been successfully reset and you are now logged in to your account.
        </p>

        <div class="success-details">
            <h3>What happened:</h3>
            <ul>
                <li>Your old password has been replaced</li>
                <li>Your account is now secured with the new password</li>
                <li>You have been automatically logged in</li>
                <li>You can now access your QuickReceipt dashboard</li>
            </ul>
        </div>

        <a href="{% url 'dashboard' %}" class="login-button">Go to Dashboard</a>

        <a href="{% url 'generate_receipt' %}" class="secondary-link">Generate New Receipt</a>

        <div class="security-note">
            <strong>Security Tip:</strong> Make sure to keep your new password secure and don't share it with anyone. 
            Consider using a password manager to store it safely.
        </div>
    </div>
</body>
</html>
