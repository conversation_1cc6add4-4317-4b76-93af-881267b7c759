/* users/static/css/styles.css */

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    color: #333;
    margin: 0;
    padding: 0;
}

h1, h2, h3 {
    color: #2c3e50;
    text-align: center; /* Centered headings */
}

.container {
    max-width: 800px;
    margin: auto;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

form {
    display: flex;
    flex-direction: column;
}

form div {
    margin-bottom: 15px;
}

label {
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="text"],
input[type="number"],
input[type="date"],
input[type="email"],
select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

input[type="submit"],
button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

input[type="submit"]:hover,
button:hover {
    background-color: #2980b9;
}

.receipt {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    background-color: #f9f9f9;
    text-align: center; /* Center the text */
}

.logo {
    width: 100px; /* Adjust width as needed */
    height: 100px;
    border-radius: 50%; /* Circular logo */
}

.button-container {
    text-align: center; /* Center the buttons */
    margin-top: 20px;
}

p {
    margin: 5px 0; /* Consistent spacing for paragraphs */
}
