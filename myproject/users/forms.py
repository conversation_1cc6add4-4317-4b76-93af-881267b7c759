# users/forms.py

from django import forms
from django.contrib.auth.models import User


class SignUpForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput, label="Password")
    confirm_password = forms.CharField(
        widget=forms.PasswordInput, label="Confirm Password"
    )

    class Meta:
        model = User
        fields = ["username", "email", "password"]

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        confirm_password = cleaned_data.get("confirm_password")

        if password and confirm_password and password != confirm_password:
            raise forms.ValidationError("Passwords do not match.")

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password"])  # Hash the password
        if commit:
            user.save()
        return user


class UpdateProfileForm(forms.Form):
    company_logo = forms.ImageField(required=False)  # For uploading company logo
    company_name = forms.CharField(max_length=255, required=True)
    company_details = forms.CharField(widget=forms.Textarea, required=True)  #
    address = forms.CharField(max_length=255, required=False)
    whatsapp_contact = forms.CharField(max_length=255, required=False)


class ProductForm(forms.Form):
    product_bought = forms.CharField(max_length=255, required=True)
    quantity = forms.IntegerField(required=True)
    price = forms.DecimalField(max_digits=10, decimal_places=2, required=True)


class ReceiptForm(forms.Form):
    customer_name = forms.CharField(max_length=255, required=True)
    products = forms.CharField(
        widget=forms.HiddenInput(), required=False
    )  # To store JSON data
    discount = forms.DecimalField(
        max_digits=10, decimal_places=2, required=False, initial=0
    )
    date_of_purchase = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))
    method_of_purchase = forms.ChoiceField(
        choices=[("cash", "Cash"), ("transfer", "Transfer")], required=True
    )


class PasswordResetRequestForm(forms.Form):
    email = forms.EmailField(
        max_length=254,
        widget=forms.EmailInput(attrs={
            'placeholder': 'Enter your email address',
            'class': 'form-control'
        }),
        help_text="Enter the email address associated with your account."
    )

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if not User.objects.filter(email=email).exists():
            raise forms.ValidationError(
                "No account found with this email address."
            )
        return email


class PasswordResetConfirmForm(forms.Form):
    new_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Enter new password',
            'class': 'form-control'
        }),
        min_length=8,
        help_text="Password must be at least 8 characters long."
    )
    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'placeholder': 'Confirm new password',
            'class': 'form-control'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        new_password = cleaned_data.get('new_password')
        confirm_password = cleaned_data.get('confirm_password')

        if new_password and confirm_password:
            if new_password != confirm_password:
                raise forms.ValidationError("Passwords do not match.")

        return cleaned_data
