from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    company_logo = models.ImageField(
        upload_to="company_logos/"
    )  # This will store uploaded logos
    company_name = models.CharField(max_length=255)
    company_details = models.TextField()
    address = models.CharField(max_length=255, blank=True, null=True)
    whatsapp_contact = models.Char<PERSON>ield(max_length=15, blank=True, null=True)

    def __str__(self):
        return self.user.username


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    instance.userprofile.save()


class Receipt(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    customer_name = models.CharField(max_length=255)
    products = models.JSONField()
    discount = models.DecimalField(max_digits=10, decimal_places=2)
    total = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateTimeField(auto_now_add=True)
    # status = models.CharField(max_length=255, default='pending')
    payment_method = models.CharField(max_length=255, default="cash")
