repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0  # Use the latest version or a specific tag
    hooks:
    -   id: check-yaml
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
-   repo: https://github.com/psf/black
    rev: 23.7.0 # Use the latest version or a specific tag
    hooks:
    -   id: black
-   repo: local
    hooks:
    -   id: pylint
        name: pylint
        entry: pylint
        language: system
        types: [python]
        require_serial: true # Necessary for accurate error reporting from pylint
        stages: [commit]
