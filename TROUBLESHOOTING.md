# Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Local Development Issues

#### 1. CSS/Static Files Not Loading
**Problem**: Styles not appearing, 404 errors for static files

**Solutions**:
```bash
# Collect static files
python manage.py collectstatic --noinput

# Check static files configuration
python manage.py findstatic users/css/style.css

# Verify STATIC_URL in settings
# Should be: STATIC_URL = '/static/'
```

#### 2. Database Migration Errors
**Problem**: Migration fails or database errors

**Solutions**:
```bash
# Reset migrations (development only)
rm users/migrations/0*.py
python manage.py makemigrations users
python manage.py migrate

# Check database connection
python manage.py dbshell
```

#### 3. Virtual Environment Issues
**Problem**: Module not found errors

**Solutions**:
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Reinstall requirements
pip install -r requirements.txt
```

### AWS EC2 Deployment Issues

#### 1. Gunicorn Service Fails
**Problem**: Gunicorn won't start or keeps crashing

**Check logs**:
```bash
sudo journalctl -u gunicorn -f
sudo systemctl status gunicorn
```

**Common fixes**:
```bash
# Check file permissions
sudo chown -R ubuntu:www-data /home/<USER>/receipt/
sudo chmod -R 755 /home/<USER>/receipt/

# Verify socket file location
ls -la /home/<USER>/receipt/myproject/myproject.sock

# Restart service
sudo systemctl restart gunicorn
```

#### 2. Nginx 502 Bad Gateway
**Problem**: Nginx can't connect to Gunicorn

**Check**:
```bash
# Verify Gunicorn is running
sudo systemctl status gunicorn

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log

# Test Nginx configuration
sudo nginx -t
```

**Fix**:
```bash
# Ensure socket file exists and has correct permissions
sudo systemctl restart gunicorn
sudo systemctl restart nginx
```

#### 3. Static Files Not Serving
**Problem**: CSS/JS files return 404 in production

**Solutions**:
```bash
# Collect static files with production settings
python manage.py collectstatic --noinput --settings=myproject.settings_production

# Check Nginx static file configuration
sudo nano /etc/nginx/sites-available/myproject

# Verify static files directory
ls -la /home/<USER>/receipt/myproject/staticfiles/
```

#### 4. Database Connection Errors
**Problem**: Can't connect to PostgreSQL

**Check**:
```bash
# Test PostgreSQL connection
sudo -u postgres psql -c "SELECT version();"

# Check if database exists
sudo -u postgres psql -l

# Test connection with credentials
psql -h localhost -U myproject -d myproject
```

**Fix**:
```bash
# Restart PostgreSQL
sudo systemctl restart postgresql

# Check .env file has correct credentials
cat .env
```

#### 5. Permission Denied Errors
**Problem**: File permission issues

**Fix**:
```bash
# Set correct ownership
sudo chown -R ubuntu:www-data /home/<USER>/receipt/

# Set correct permissions
sudo chmod -R 755 /home/<USER>/receipt/
sudo chmod -R 644 /home/<USER>/receipt/myproject/staticfiles/
```

### SSL Certificate Issues

#### 1. Certbot Fails
**Problem**: SSL certificate generation fails

**Solutions**:
```bash
# Ensure domain points to your server
nslookup your-domain.com

# Check if port 80 is accessible
sudo ufw status
sudo ufw allow 80
sudo ufw allow 443

# Try manual certificate generation
sudo certbot certonly --nginx -d your-domain.com
```

### Performance Issues

#### 1. Slow Loading Times
**Solutions**:
```bash
# Enable Nginx gzip compression
sudo nano /etc/nginx/nginx.conf
# Add: gzip on; gzip_types text/css application/javascript;

# Optimize static files
python manage.py collectstatic --noinput

# Check server resources
htop
df -h
```

## 🔍 Debugging Commands

### Check Service Status
```bash
# All services
sudo systemctl status gunicorn nginx postgresql

# Individual services
sudo systemctl status gunicorn
sudo systemctl status nginx
sudo systemctl status postgresql
```

### View Logs
```bash
# Gunicorn logs
sudo journalctl -u gunicorn -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Django logs (if configured)
tail -f /var/log/django/myproject.log
```

### Test Connections
```bash
# Test Gunicorn directly
curl http://localhost:8000

# Test through Nginx
curl http://your-domain.com

# Test static files
curl http://your-domain.com/static/users/css/style.css
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the logs** using the commands above
2. **Search existing issues** on GitHub
3. **Create a new issue** with:
   - Error messages
   - Steps to reproduce
   - System information
   - Log outputs

## 🔧 Emergency Recovery

### Reset Gunicorn Service
```bash
sudo systemctl stop gunicorn
sudo rm /home/<USER>/receipt/myproject/myproject.sock
sudo systemctl start gunicorn
```

### Reset Nginx
```bash
sudo systemctl stop nginx
sudo nginx -t
sudo systemctl start nginx
```

### Database Backup and Restore
```bash
# Backup
pg_dump -U myproject -h localhost myproject > backup.sql

# Restore
psql -U myproject -h localhost myproject < backup.sql
```

Remember: Always backup your data before making major changes! 🛡️
