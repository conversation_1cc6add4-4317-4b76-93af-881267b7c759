# Quick Receipt 🧾

A Django-based web application for generating professional receipts quickly and efficiently. Perfect for small businesses, freelancers, and anyone who needs to create branded receipts on the go.

## ✨ Features

- **🚀 Quick Receipt Generation** - Create professional receipts in seconds
- **🎨 Custom Branding** - Upload your company logo and customize receipt appearance
- **📱 Responsive Design** - Works seamlessly on desktop and mobile devices
- **👤 User Management** - Secure user registration and profile management
- **💰 Multiple Payment Methods** - Support for various payment types
- **📊 Product Management** - Add multiple products with quantities and pricing
- **🧮 Automatic Calculations** - Built-in discount and total calculations
- **📄 Professional Templates** - Clean, business-ready receipt layouts

## 🛠️ Tech Stack

- **Backend**: Django 5.2.1
- **Frontend**: Bootstrap 5.3.3, HTML5, CSS3, JavaScript
- **Database**: SQLite (development), PostgreSQL (production)
- **Static Files**: WhiteNoise / AWS S3 + CloudFront
- **Deployment**: AWS EC2, Gunicorn, Nginx

## 📋 Prerequisites

- Python 3.8+
- Git
- Virtual environment (recommended)

## 🚀 Quick Start (Local Development)

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd receipt/myproject
```

### 2. Set Up Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
```bash
# Copy sample environment file
cp ../sample.env .env

# Edit .env with your settings
SECRET_KEY=your-secret-key-here
DEBUG=True
POSTGRES_NAME=myproject
POSTGRES_USER=myproject
POSTGRES_PASSWORD=myproject
POSTGRES_HOST=localhost
```

### 5. Database Setup
```bash
python manage.py migrate
python manage.py createsuperuser  # Optional: create admin user
```

### 6. Collect Static Files
```bash
python manage.py collectstatic --noinput
```

### 7. Run Development Server
```bash
# Option 1: Use the startup script
./run_local.sh

# Option 2: Manual start
python manage.py runserver 0.0.0.0:8000
```

Visit `http://localhost:8000` to see your application!

## 🌐 AWS EC2 Deployment

### Prerequisites
- AWS Account with EC2 access
- Domain name (optional but recommended)
- Basic knowledge of Linux commands

### Step 1: Launch EC2 Instance

1. **Create EC2 Instance**:
   - AMI: Ubuntu 22.04 LTS
   - Instance Type: t3.micro (free tier) or t3.small
   - Security Group: Allow HTTP (80), HTTPS (443), SSH (22)
   - Key Pair: Create or use existing

2. **Connect to Instance**:
```bash
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

### Step 2: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3-pip python3-venv nginx postgresql postgresql-contrib git

# Install Node.js (for npm packages if needed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Step 3: Database Setup

```bash
# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
```

In PostgreSQL shell:
```sql
CREATE DATABASE myproject;
CREATE USER myproject WITH PASSWORD 'your-secure-password';
ALTER ROLE myproject SET client_encoding TO 'utf8';
ALTER ROLE myproject SET default_transaction_isolation TO 'read committed';
ALTER ROLE myproject SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE myproject TO myproject;
\q
```

### Step 4: Application Deployment

```bash
# Clone repository
cd /home/<USER>
git clone <your-repository-url> receipt
cd receipt/myproject

# Set up virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create production environment file
sudo nano .env
```

Add to `.env`:
```bash
SECRET_KEY=your-very-secure-secret-key-here
DEBUG=False
POSTGRES_NAME=myproject
POSTGRES_USER=myproject
POSTGRES_PASSWORD=your-secure-password
POSTGRES_HOST=localhost
```

```bash
# Run migrations
python manage.py migrate --settings=myproject.settings_production

# Collect static files
python manage.py collectstatic --noinput --settings=myproject.settings_production

# Create superuser
python manage.py createsuperuser --settings=myproject.settings_production

# Test the application
python manage.py runserver 0.0.0.0:8000 --settings=myproject.settings_production
```

### Step 5: Gunicorn Setup

```bash
# Test Gunicorn
gunicorn --bind 0.0.0.0:8000 myproject.wsgi:application --settings=myproject.settings_production

# Create Gunicorn service
sudo nano /etc/systemd/system/gunicorn.service
```

Add to `gunicorn.service`:
```ini
[Unit]
Description=gunicorn daemon
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/home/<USER>/receipt/myproject
ExecStart=/home/<USER>/receipt/myproject/venv/bin/gunicorn \
          --access-logfile - \
          --workers 3 \
          --bind unix:/home/<USER>/receipt/myproject/myproject.sock \
          myproject.wsgi:application
Environment=DJANGO_SETTINGS_MODULE=myproject.settings_production

[Install]
WantedBy=multi-user.target
```

```bash
# Start and enable Gunicorn
sudo systemctl start gunicorn
sudo systemctl enable gunicorn
sudo systemctl status gunicorn
```

### Step 6: Nginx Configuration

```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/myproject
```

Add to `myproject`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com your-ec2-public-ip;

    location = /favicon.ico { access_log off; log_not_found off; }

    location /static/ {
        root /home/<USER>/receipt/myproject;
    }

    location /media/ {
        root /home/<USER>/receipt/myproject;
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/home/<USER>/receipt/myproject/myproject.sock;
    }
}
```

```bash
# Enable site and restart Nginx
sudo ln -s /etc/nginx/sites-available/myproject /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx

# Allow Nginx through firewall
sudo ufw allow 'Nginx Full'
```

### Step 7: SSL Certificate (Optional but Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Step 8: Final Configuration

Update your Django settings in `settings_production.py`:
```python
ALLOWED_HOSTS = [
    'your-domain.com',
    'www.your-domain.com',
    'your-ec2-public-ip',
]
```

```bash
# Restart services
sudo systemctl restart gunicorn
sudo systemctl restart nginx
```

## 🔧 Maintenance Commands

```bash
# Update application
cd /home/<USER>/receipt/myproject
git pull
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate --settings=myproject.settings_production
python manage.py collectstatic --noinput --settings=myproject.settings_production
sudo systemctl restart gunicorn

# View logs
sudo journalctl -u gunicorn
sudo tail -f /var/log/nginx/error.log

# Backup database
pg_dump -U myproject -h localhost myproject > backup_$(date +%Y%m%d).sql
```

## 📁 Project Structure

```
myproject/
├── manage.py
├── requirements.txt
├── run_local.sh
├── myproject/
│   ├── settings.py              # Development settings
│   ├── settings_production.py   # Production settings
│   ├── settings_cdn.py         # CDN-enabled settings
│   ├── urls.py
│   └── wsgi.py
├── users/
│   ├── models.py
│   ├── views.py
│   ├── forms.py
│   ├── static/users/
│   └── templates/users/
├── static/                     # Global static files
├── staticfiles/               # Collected static files (don't commit)
└── media/                     # User uploads
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Abiodun Aderounmu** - Product Manager
- **Fuhad Yusuf** - Chief Technology Officer

## 🆘 Support

If you encounter any issues:

1. Check the [troubleshooting guide](TROUBLESHOOTING.md)
2. Review server logs: `sudo journalctl -u gunicorn`
3. Open an issue on GitHub
4. Contact the development team

---

**Quick Receipt** - Making business receipts quick and professional! 🚀
